'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Database,
  Search,
  RefreshCw,
  HardDrive,
  Activity,
  FileText,
  Layers,
  BarChart3,
  Download,
  Upload,
  Trash2,
  Eye,
} from 'lucide-react';
import { formatBytes, formatDate } from '@/lib/utils';
import { toast } from 'sonner';

interface DatabaseStats {
  totalDocuments: number;
  totalChunks: number;
  totalSize: number;
  indexSize: number;
  collections: number;
  lastUpdated: string;
}

interface VectorCollection {
  name: string;
  documents: number;
  vectors: number;
  dimensions: number;
  size: string;
  status: 'healthy' | 'degraded' | 'error';
  lastUpdated: string;
}

interface DocumentChunk {
  id: string;
  content: string;
  source: string;
  metadata: Record<string, any>;
  embedding_size: number;
  created_at: string;
}

export default function DatabasePage() {
  const [stats, setStats] = useState<DatabaseStats>({
    totalDocuments: 0,
    totalChunks: 0,
    totalSize: 0,
    indexSize: 0,
    collections: 0,
    lastUpdated: new Date().toISOString(),
  });

  // Safe number formatting function
  const safeFormatNumber = (value: number | undefined | null): string => {
    if (value === undefined || value === null || isNaN(value)) {
      return '0';
    }
    return value.toLocaleString();
  };
  const [collections, setCollections] = useState<VectorCollection[]>([]);
  const [documents, setDocuments] = useState<DocumentChunk[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<DocumentChunk[]>([]);
  const [searching, setSearching] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');

  const loadDatabaseData = async () => {
    setLoading(true);
    try {
      // Load database stats
      const statsResponse = await fetch('http://localhost:8000/admin/database/stats');
      const statsData = await statsResponse.json();
      setStats(statsData);

      // Load collections
      const collectionsResponse = await fetch('http://localhost:8000/admin/database/collections');
      const collectionsData = await collectionsResponse.json();
      setCollections(collectionsData.collections);

      // Load documents
      const documentsResponse = await fetch('http://localhost:8000/admin/database/documents?limit=50');
      const documentsData = await documentsResponse.json();
      setDocuments(documentsData.documents);

      toast.success('Database data loaded successfully');
    } catch (error: any) {
      toast.error('Failed to load database data: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setSearchResults([]);
      return;
    }

    setSearching(true);
    try {
      const response = await fetch('http://localhost:8000/admin/database/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchTerm,
          limit: 10
        })
      });

      const data = await response.json();
      setSearchResults(data.results);
      toast.success(`Found ${data.total_found} matching documents`);
    } catch (error: any) {
      toast.error('Search failed: ' + error.message);
    } finally {
      setSearching(false);
    }
  };

  const handleDeleteCollection = async (collectionName: string) => {
    if (!confirm(`Are you sure you want to delete collection "${collectionName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:8000/admin/database/collections/${collectionName}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success(`Collection "${collectionName}" deleted successfully`);
        loadDatabaseData(); // Reload data
      } else {
        throw new Error('Failed to delete collection');
      }
    } catch (error: any) {
      toast.error('Failed to delete collection: ' + error.message);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    if (!confirm(`Are you sure you want to delete document "${documentId}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`http://localhost:8000/admin/database/documents/${documentId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        toast.success(`Document "${documentId}" deleted successfully`);
        loadDatabaseData(); // Reload data
      } else {
        throw new Error('Failed to delete document');
      }
    } catch (error: any) {
      toast.error('Failed to delete document: ' + error.message);
    }
  };

  const handleCreateCollection = async () => {
    if (!newCollectionName.trim()) {
      toast.error('Please enter a collection name');
      return;
    }

    try {
      const response = await fetch('http://localhost:8000/admin/database/collections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: newCollectionName.trim()
        })
      });

      if (response.ok) {
        toast.success(`Collection "${newCollectionName}" created successfully`);
        setNewCollectionName('');
        loadDatabaseData(); // Reload data
      } else {
        throw new Error('Failed to create collection');
      }
    } catch (error: any) {
      toast.error('Failed to create collection: ' + error.message);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy':
        return <Badge className="bg-green-100 text-green-800">Healthy</Badge>;
      case 'degraded':
        return <Badge className="bg-yellow-100 text-yellow-800">Degraded</Badge>;
      case 'error':
        return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  useEffect(() => {
    loadDatabaseData();
  }, []);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Database Management</h1>
          <p className="text-gray-600 mt-1">
            Manage vector database, collections, and document chunks
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={loadDatabaseData} variant="outline" disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Database Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Documents</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{safeFormatNumber(stats.totalDocuments)}</div>
            <p className="text-xs text-muted-foreground">
              Indexed documents
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Vector Chunks</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{safeFormatNumber(stats.totalChunks)}</div>
            <p className="text-xs text-muted-foreground">
              Embedded chunks
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Database Size</CardTitle>
            <HardDrive className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatBytes(stats.totalSize)}</div>
            <p className="text-xs text-muted-foreground">
              Total storage used
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Collections</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.collections}</div>
            <p className="text-xs text-muted-foreground">
              Vector collections
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="collections" className="space-y-4">
        <TabsList>
          <TabsTrigger value="collections">Collections</TabsTrigger>
          <TabsTrigger value="search">Vector Search</TabsTrigger>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Collections Tab */}
        <TabsContent value="collections" className="space-y-4">
          {/* Create Collection */}
          <Card>
            <CardHeader>
              <CardTitle>Create New Collection</CardTitle>
              <CardDescription>
                Add a new vector collection to your database
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-2">
                <Input
                  placeholder="Enter collection name..."
                  value={newCollectionName}
                  onChange={(e) => setNewCollectionName(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleCreateCollection()}
                  className="flex-1"
                />
                <Button onClick={handleCreateCollection} disabled={!newCollectionName.trim()}>
                  Create Collection
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Vector Collections</CardTitle>
              <CardDescription>
                Manage your vector database collections and their status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Collection Name</TableHead>
                    <TableHead>Documents</TableHead>
                    <TableHead>Vectors</TableHead>
                    <TableHead>Dimensions</TableHead>
                    <TableHead>Size</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {collections.map((collection) => (
                    <TableRow key={collection.name}>
                      <TableCell className="font-medium">{collection.name}</TableCell>
                      <TableCell>{safeFormatNumber(collection.documents)}</TableCell>
                      <TableCell>{safeFormatNumber(collection.vectors)}</TableCell>
                      <TableCell>{collection.dimensions}</TableCell>
                      <TableCell>{collection.size}</TableCell>
                      <TableCell>{getStatusBadge(collection.status)}</TableCell>
                      <TableCell>{formatDate(collection.lastUpdated)}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline" title="View Collection">
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="outline" title="Refresh Collection">
                            <RefreshCw className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:bg-red-50"
                            title="Delete Collection"
                            onClick={() => handleDeleteCollection(collection.name)}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Vector Search Tab */}
        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Vector Search</CardTitle>
              <CardDescription>
                Search through your vector database using semantic similarity
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <div className="flex-1">
                  <Input
                    placeholder="Enter search query..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <Button onClick={handleSearch} disabled={searching}>
                  {searching ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Search className="w-4 h-4" />
                  )}
                </Button>
              </div>

              {searchResults.length > 0 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-medium">Search Results ({searchResults.length})</h3>
                  {searchResults.map((result) => (
                    <Card key={result.id}>
                      <CardContent className="pt-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Badge variant="outline">{result.source}</Badge>
                            <span className="text-sm text-gray-500">{formatDate(result.created_at)}</span>
                          </div>
                          <p className="text-sm">{result.content}</p>
                          <div className="text-xs text-gray-500">
                            ID: {result.id} | Embedding Size: {result.embedding_size}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Documents Tab */}
        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Document Chunks</CardTitle>
              <CardDescription>
                Browse and manage individual document chunks in the database
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Content Preview</TableHead>
                    <TableHead>Source</TableHead>
                    <TableHead>Metadata</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {documents.map((doc) => (
                    <TableRow key={doc.id}>
                      <TableCell className="font-mono text-sm">{doc.id}</TableCell>
                      <TableCell className="max-w-xs truncate">{doc.content}</TableCell>
                      <TableCell>{doc.source}</TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {Object.keys(doc.metadata).length} fields
                        </Badge>
                      </TableCell>
                      <TableCell>{formatDate(doc.created_at)}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline" title="View Document">
                            <Eye className="w-3 h-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            className="text-red-600 hover:bg-red-50"
                            title="Delete Document"
                            onClick={() => handleDeleteDocument(doc.id)}
                          >
                            <Trash2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Storage Usage</CardTitle>
                <CardDescription>Database storage breakdown</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Documents</span>
                    <span className="text-sm text-gray-600">{formatBytes(stats.totalSize * 0.7)}</span>
                  </div>
                  <Progress value={70} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Indexes</span>
                    <span className="text-sm text-gray-600">{formatBytes(stats.indexSize)}</span>
                  </div>
                  <Progress value={20} className="h-2" />
                </div>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Metadata</span>
                    <span className="text-sm text-gray-600">{formatBytes(stats.totalSize * 0.1)}</span>
                  </div>
                  <Progress value={10} className="h-2" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>Database performance indicators</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Average Query Time</span>
                  <span className="text-sm text-gray-600">45ms</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Cache Hit Rate</span>
                  <span className="text-sm text-gray-600">85%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Index Efficiency</span>
                  <span className="text-sm text-gray-600">92%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Compression Ratio</span>
                  <span className="text-sm text-gray-600">3.2:1</span>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
